// Fixed batch processing section with proper indentation
for (let i = 0; i < invoiceIds.length; i += batchSize) {
  const batchInvoiceIds = invoiceIds.slice(i, i + batchSize);
  const batchInvoices = batchInvoiceIds.map(invoiceId => itemsGroupedByInvoiceId[invoiceId]);

  logger.log(`Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(invoiceIds.length / batchSize)} (${batchInvoices.length} invoices)`);

  // Create a new session for this batch
  session = await retryTransientErrors(async () => {
    const newSession = await mongoose.startSession();
    newSession.startTransaction();
    return newSession;
  });

  try {
    const rawUserIds = new Set();
    const rawPricingIds = new Set();
    const rawCustomPackageIds = new Set();
    const rawProductIds = new Set();
    const rawVoucherCodes = new Set<string>();

    // prepare data
    for (const invoice of batchInvoices) {
      invoice.forEach(csvItem => {
        if ((csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE || csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER)) {
          allPricingIds.add(csvItem.itemId);
          rawPricingIds.add(csvItem.itemId);
        } else if (csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE) {
          rawCustomPackageIds.add(csvItem.itemId);
          allCustomPackageIds.add(csvItem.itemId);
        } else if (csvItem.itemType.trim() == ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
          allProductIds.add(csvItem.itemId);
          rawProductIds.add(csvItem.itemId);
        } else {
          logger.error(`Row ${csvItem.index + 1}: Invalid item type ${csvItem.itemType} for invoice ${invoice[0].invoiceId}`);
          throw new Error(` ${csvItem.index + 1}: Invalid item type ${csvItem.itemType} for invoice ${invoice[0].invoiceId}`);
        }
        if (!getAllPaymentMethodIds.has(csvItem.paymentMethod)) {
          getAllPaymentMethodIds.add(csvItem.paymentMethod);
        }
        if (csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER) {
          rawVoucherCodes.add(csvItem.voucherCode);
        }
      });
      rawUserIds.add(invoice[0].userId);
      allUserIds.add(invoice[0].userId);
      if (invoice[0].billingClientId) {
        rawUserIds.add(invoice[0].billingClientId);
        allUserIds.add(invoice[0].billingClientId);
      }
    }

    // ... rest of batch processing logic ...

    // Process invoices in this batch
    for (const invoiceRawData of batchInvoices) {
      // ... invoice processing logic ...
      
      // Save invoice with retry logic for transient transaction errors
      await retryTransientErrors(async () => {
        return await invoice.save({ session });
      });
    }

    // Commit the transaction for this batch with retry logic
    await retryTransientErrors(async () => {
      await session.commitTransaction();
    });
    logger.log(`Batch ${Math.floor(i / batchSize) + 1} transaction committed successfully`);

  } catch (error) {
    // Abort the transaction on error for this batch
    logger.error(`Error in batch ${Math.floor(i / batchSize) + 1} processing:`, error);
    if (session) {
      try {
        await session.abortTransaction();
      } catch (abortError) {
        logger.error('Error aborting batch transaction:', abortError);
      }
    }
    throw error;
  } finally {
    // End the session for this batch
    if (session) {
      try {
        await session.endSession();
      } catch (endError) {
        logger.error('Error ending batch session:', endError);
      }
    }
  }
}
