import mongoose, { Document, Schema, Types } from "mongoose";
import { DaysOfWeek } from '../../utils/enums/days-of-week.enum';
import { ENUM_DISCOUNT_TYPE, ENUM_DURATION_UNIT, ENUM_PRODUCT_ITEM_TYPE } from "../../common/enums/enums";

// Define enums
export enum ClassType {
  PERSONAL_APPOINTMENT = "personalAppointment",
  CLASSES = "classes",
  BOOKINGS = "bookings",
  COURSES = "courses",
}

// export enum DiscountType {
//   FLAT = 'Flat',
//   PERCENTAGE = 'Percentage'
// }

export enum SessionType {
  SINGLE = "single",
  MULTIPLE = "multiple",
  UNLIMITED = "unlimited",
  DAY_PASS = 'day_pass',
  ONCEADAY = "once_a_day",
}

export enum DurationUnit {
  DAYS = 'days',
  WEEKS = 'weeks',
  MONTHS = 'months',
  YEARS = 'years'
}

// Define interfaces
export interface IActiveTimeFrame {
  date?: Date;
  dayOfWeek?: DaysOfWeek;
  startTime: string;
  endTime: string;
}

export interface IDiscount {
  type?: ENUM_DISCOUNT_TYPE;
  value?: number;
}

export interface IRelationship {
  serviceCategory?: Types.ObjectId;
  subTypeIds?: Types.ObjectId[];
}

export interface IServices {
  type: ClassType;
  serviceCategory: Types.ObjectId;
  appointmentType?: Types.ObjectId[];
  sessionType: SessionType;
  sessionCount: number;
  dayPassLimit: number;
  sessionPerDay: number;
  introductoryOffer: string;
  relationShip?: IRelationship[];
}

export interface IPricing extends Document {
  _id: Types.ObjectId;
  id: string;
  invoiceItemId: string;
  createdBy: Types.ObjectId;
  organizationId: Types.ObjectId;
  itemType: ENUM_PRODUCT_ITEM_TYPE;
  name: string;
  price: number;
  isSellOnline: boolean;
  tax: number;
  services?: IServices;
  expiredInDays: number;
  hsnOrSacCode?: string;
  durationUnit: ENUM_DURATION_UNIT;
  membershipId?: Types.ObjectId;
  // discount?: IDiscount;
  promotion?: Types.ObjectId;
  isActive: boolean;
  pricingIds?: Types.ObjectId[];
  isBundledPricing: boolean;
  revenueCategory?: Types.ObjectId;
  activeTimeFrames?: IActiveTimeFrame[];
  description?: string;
  shortDescription?: string;
  image?: string;
  isFeatured?: boolean;
  isTrial?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  voucherCode?: string;
}

// Define schemas
const ActiveTimeFrameSchema = new Schema<IActiveTimeFrame>({
  date: {
    type: Date,
    required: false,
    default: null
  },
  dayOfWeek: {
    enum: Object.values(DaysOfWeek),
    type: String,
    required: false
  },
  startTime: {
    type: String,
    validate: {
      validator: function (v: string) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
      },
      message: (props: any) => `${props.value} is not a valid time format. Please use HH:mm format.`
    }
  },
  endTime: {
    type: String,
    validate: {
      validator: function (v: string) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
      },
      message: (props: any) => `${props.value} is not a valid time format. Please use HH:mm format.`
    }
  }
}, { timestamps: false });


const ServicesSchema = new Schema<IServices>({
  type: {
    type: String,
    enum: Object.values(ClassType),
    required: true
  },
  serviceCategory: {
    type: Schema.Types.ObjectId,
    ref: "Service",
    required: true
  },
  appointmentType: {
    type: [Schema.Types.ObjectId],
    ref: "Attributes",
    default: []
  },
  relationShip: [
    {
      serviceCategory: {
        type: Schema.Types.ObjectId,
        ref: "Service",
        required: false
      },
      subTypeIds: {
        type: [Schema.Types.ObjectId],
        ref: "Attributes",
        required: false
      }
    }
  ],
  sessionType: {
    type: String,
    enum: Object.values(SessionType),
    required: true
  },
  sessionCount: {
    type: Number,
    required: true
  },
  dayPassLimit: {
    type: Number,
    required: true
  },
  sessionPerDay: {
    type: Number,
    required: true
  },
  introductoryOffer: {
    type: String,
    required: false
  }
}, { _id: false });

// Define Pricing schema
const PricingSchema = new Schema<IPricing>({
  id: {
    type: String,
    required: false
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: "User"
  },
  organizationId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: "Organization"
  },
  itemType: {
    type: String,
    enum: Object.values(ENUM_PRODUCT_ITEM_TYPE),
    required: true
  },
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  isSellOnline: {
    type: Boolean,
    required: false,
    default: true
  },
  tax: {
    type: Number,
    required: false,
    default: 0
  },
  services: {
    type: ServicesSchema,
    required: false
  },
  expiredInDays: {
    type: Number,
    required: true
  },
  hsnOrSacCode: {
    type: String,
    required: false
  },
  durationUnit: {
    type: String,
    required: true,
    enum: Object.values(DurationUnit)
  },
  membershipId: {
    type: Schema.Types.ObjectId,
    required: false
  },
  // discount: {
  //   type: DiscountSchema,
  //   required: false
  // },
  promotion: {
    type: Schema.Types.ObjectId,
    ref: "Promotion",
    default: null,
    required: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  pricingIds: {
    type: [Schema.Types.ObjectId],
    ref: "Pricing",
    required: false
  },
  isBundledPricing: {
    type: Boolean,
    default: false
  },
  revenueCategory: {
    type: Schema.Types.ObjectId,
    required: false,
    ref: "organization.revenueCategory"
  },
  shortDescription: {
    type: String,
    required: false,
    trim: true,
  },
  description: {
    type: String,
    required: false,
    trim: true,
  },
  image: {
    type: String,
    required: false,
    trim: true,
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  isTrial: {
    type: Boolean,
    default: false
  },
  activeTimeFrames: {
    type: [ActiveTimeFrameSchema],
    default: [],
    required: false
  },
  voucherCode: {
    type: String,
    required: false,
    trim: true,
  }
}, {
  timestamps: true
});

// Create and export the model
export const PRICING_COLLECTION = 'pricings';
export const Pricing = mongoose.model<IPricing>('Pricing', PricingSchema, PRICING_COLLECTION);

PricingSchema.pre('save', function (next) {
  delete this.voucherCode;
  delete this.invoiceItemId;

  next();
});
