import { Command } from 'commander';
import { migrateServices } from '../modules/pricing/service-category.migration';

export function createServiceCommand(): Command {
  const serviceCommand = new Command('service');

  serviceCommand
    .description('Migrate service data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateServices(options.database);
      } catch (error) {
        console.error('Error executing service migration command:', error);
        process.exit(1);
      }
    });

  return serviceCommand;
}
