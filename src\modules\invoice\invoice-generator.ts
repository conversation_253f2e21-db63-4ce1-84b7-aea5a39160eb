import { SaveOptions, Types } from 'mongoose';
import mongoose from 'mongoose';
import { ENUM_DISCOUNT_TYPE, ENUM_DURATION_UNIT, ENUM_PRODUCT_ITEM_TYPE } from '../../common/enums/enums';
import * as numberToWords from 'number-to-words';
import { IPricing } from '../pricing/pricing.model';
import { IPurchase, Purchase } from './purchase.model';
import { IInvoice, IInvoicePurchaseItem, Invoice, IInvoiceProductItem, IInvoiceCustomPackageItem, IInvoiceReturnItems } from './invoice.model';
import { IProduct } from '../products/products.model';
import { IInventory } from '../products/inventory.model';
import { ICustomPackageDocument } from '../pricing/custom-package.model';
import { IVoucherRedemption, VoucherRedemption } from './voucherRedemption.model';

export interface calculation {
    price: number;
    unitPrice: number;
    quantity: number;
    totalPrice?: number;
    itemDiscountType?: string;
    discountExcludeCart?: number;
    discountIncludeCart?: number;
    taxAmount?: number;
    taxRate: number;
    totalDiscountAmount?: number;
    discountedPrice?: number;
    finalPrice?: number;
}

export interface InvoiceGeneratorOptions {
    userId: string;
    organizationId: string;
    facilityId: string;
    createdBy?: string;
    paymentBy?: string;
    invoiceNumber: number;
    orderId: number;
    platform: string;
    date?: Date;
    isInclusiveofGst?: boolean;
    billingAddressId: string;
    clientDetails: any;
    clientBillingDetails: any;
    billingDetails: any;
    paymentDetails?: any;
    isForBusiness?: boolean;
    cartDiscount?: number;
    cartDiscountType?: string;
    voucherDiscount?: number;
    returnDiscount?: number;
    discountedBy?: string;
}

export interface IPurchaseItemOptions {
    startDate?: Date;
    isInclusiveofGst?: boolean;
    discountType?: ENUM_DISCOUNT_TYPE;
    discountValue?: number;
    discountedBy?: string;
    promotionLabel?: string;
    promotionLabelKey?: string;
}


export interface ProductItemData {
    inventoryDocument: any; // Inventory document
    productDocument: any; // Product document
    quantity: number;
    variantId?: string;
    discountType?: ENUM_DISCOUNT_TYPE;
    discountValue?: number;
    discountedBy?: string;
    discountAmount?: number;
    cartDiscountAmount?: number;
    taxRate?: number;
    taxAmount?: number;
    hsnOrSacCode?: string;
    salePrice?: number;
    mrp?: number;
    finalPrice?: number;
}

export interface CustomPackageItemData {
    document: any; // Custom package document
    quantity: number;
    discountType?: ENUM_DISCOUNT_TYPE;
    discountValue?: number;
    discountedBy?: string;
    discountAmount?: number;
    cartDiscountAmount?: number;
    taxRate?: number;
    taxAmount?: number;
    hsnOrSacCode?: string;
    unitPrice?: number;
}

export class InvoiceGenerator {
    public invoice: IInvoice = null;
    public purchaseItems: IPurchase[] = [];
    public voucherRedemptions: IVoucherRedemption[] = [];
    public isCalculated: boolean = false;
    public appliedVoucherItem: IPurchase = null;

    constructor(private options: InvoiceGeneratorOptions) {
        this.initializeInvoice();
    }

    private initializeInvoice(): void {
        this.invoice = new Invoice({
            createdBy: this.options.createdBy,
            invoiceNumber: this.options.invoiceNumber,
            orderId: this.options.orderId,
            userId: this.options.userId,
            organizationId: this.options.organizationId,
            facilityId: this.options.facilityId,
            paymentBy: this.options.paymentBy ?? this.options.createdBy,
            invoiceDate: this.options.date || new Date(),
            platform: this.options.platform,
            isInclusiveofGst: this.options.isInclusiveofGst ?? true,
            clientDetails: this.options.clientDetails,
            clientBillingDetails: this.options.clientBillingDetails,
            billingDetails: this.options.billingDetails,
            paymentDetails: this.options.paymentDetails,
            isForBusiness: this.options.isForBusiness || false,
            voucherDiscount: this.options.voucherDiscount || 0,
            returnDiscount: this.options.returnDiscount || 0,
            discountedBy: this.options.discountedBy,
            cartDiscount: this.options.cartDiscount || 0,
            cartDiscountType: this.options.cartDiscountType,
            purchaseItems: [],
            productItem: [],
            customPackageItems: [],
            returnItems: [],
        });
    }


    addServiceOrVoucherItem(itemData: IPricing, quantity: number, options?: IPurchaseItemOptions): IPurchase[] {
        const { _id, name, price, itemType, tax, hsnOrSacCode, voucherCode, expiredInDays, durationUnit, membershipId, promotion, pricingIds, isBundledPricing, services } = itemData;
        const { startDate, isInclusiveofGst: isInclusiveOfGst, discountType, discountValue, discountedBy, promotionLabel, promotionLabelKey } = options || {
            startDate: this.invoice.invoiceDate,
            isInclusiveofGst: this.invoice.isInclusiveofGst,
        };

        const endDate = this.calculateEndDate(startDate, expiredInDays, 'days'); // durationUnit);
        const isExpired = endDate < new Date();

        // Create purchase item
        let newPurchaseItems: IPurchase[] = [];
        for (let i = 0; i < quantity; i++) {
            const purchaseItem = new Purchase({
                invoiceItemId: itemData.invoiceItemId,
                invoiceId: this.invoice._id,
                userId: this.options.userId,
                consumers: [this.options.userId],
                itemType: itemType,
                packageId: _id,
                bundledPricingId: null,
                organizationId: this.options.organizationId,
                facilityId: this.options.facilityId,
                purchasedBy: this.options.paymentBy ?? this.options.createdBy,
                membershipId: membershipId || null,
                voucherAmount: itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER ? price : undefined,
                amountConsumed: itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER ? 0 : undefined,
                voucherCode: itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER ? voucherCode : undefined,
                purchaseDate: this.invoice.invoiceDate,
                paymentStatus: this.invoice.paymentStatus,
                isExpired: isExpired,
                sessionType: itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE ? services?.sessionType : undefined,
                sessionPerDay: itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE ? services?.sessionPerDay || 1 : undefined,
                dayPassLimit: itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE ? services?.dayPassLimit || 0 : undefined,
                totalSessions: itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE ? services?.sessionCount || 0 : undefined,
                startDate,
                endDate,
            });
            newPurchaseItems.push(purchaseItem);
            this.purchaseItems.push(purchaseItem);
        }

        const { unitPrice, totalPrice, discountAmount, cartDiscountAmount, taxAmount, finalPrice } = this.calculateItemPricing({
            quantity,
            price: (price * quantity) || 0,
            taxRate: tax || 0,
            itemDiscount: {
                type: discountType,
                value: discountValue || 0,
            },
        });

        const invoicePurchaseItem: IInvoicePurchaseItem = {
            packageId: _id,
            purchaseIds: this.purchaseItems.map(item => item._id as Types.ObjectId),
            packageName: name,
            quantity,
            isBundledPricing: isBundledPricing || false,
            expireIn: expiredInDays,
            durationUnit: durationUnit,
            startDate,
            endDate,
            price: price,
            unitPrice: unitPrice,
            discountType: discountType && (discountValue || 0) > 0 ? discountType : undefined,
            discountValue: (discountValue || 0) > 0 ? discountValue : undefined,
            discountedBy: discountedBy && (discountValue || 0) > 0 ? new Types.ObjectId(discountedBy) : undefined,
            discountExcludeCart: discountAmount || 0,
            discountIncludeCart: 0,
            hsnOrSacCode: hsnOrSacCode || "",
            finalPrice: finalPrice,
            tax: tax || 0,
            gstAmount: (taxAmount || 0),
            promotionLabel,
            promotionLabelKey,
        };

        this.invoice.purchaseItems.push(invoicePurchaseItem);
        return newPurchaseItems;
    }

    addProductItem(product: IProduct, inventory: {
        salePrice: number;
        productVariantId?: Types.ObjectId;
    }, quantity: number, variantId?: string, options?: IPurchaseItemOptions): void {
        // addProductItem(product: IProduct, inventory: IInventory, quantity: number, variantId?: string, options?: IPurchaseItemOptions): void {

        const { salePrice } = inventory;
        const { hsn: hsnOrSacCode, gst: taxRate } = product;
        const { isInclusiveofGst: isInclusiveOfGst, discountType, discountValue, discountedBy, promotionLabel, promotionLabelKey } = options || {
            startDate: this.invoice.invoiceDate,
            isInclusiveofGst: this.invoice.isInclusiveofGst,
            discountedBy: undefined,
            promotionLabel: undefined,
            promotionLabelKey: undefined,
        };

        const { price, unitPrice, totalPrice, discountAmount, cartDiscountAmount, taxAmount, finalPrice } = this.calculateItemPricing({
            quantity,
            price: (salePrice * quantity) || 0,
            taxRate: taxRate || 0,
            itemDiscount: {
                type: discountType,
                value: discountValue || 0,
            },
        });

        const productItem: IInvoiceProductItem = {
            productId: (product._id) as Types.ObjectId,
            productVariantId: (variantId || inventory.productVariantId) as Types.ObjectId,
            productName: product.name,
            quantity,
            price: price,
            unitPrice: unitPrice,
            salePrice: salePrice || 0,
            finalPrice: finalPrice,
            discountType: discountType && (discountValue || 0) > 0 ? discountType : undefined,
            discountValue: (discountValue || 0) > 0 ? discountValue : undefined,
            discountedBy: discountedBy && (discountValue || 0) > 0 ? new Types.ObjectId(discountedBy) : undefined,
            discountExcludeCart: discountAmount || 0,
            discountIncludeCart: cartDiscountAmount || 0,
            hsnOrSacCode: hsnOrSacCode || "",
            tax: taxRate || 0,
            gstAmount: taxAmount || 0,
            promotionLabel: promotionLabel,
            promotionLabelKey: promotionLabelKey,
        };

        this.invoice.productItem.push(productItem);
    }

    addCustomPackageItem(itemData: ICustomPackageDocument, options?: IPurchaseItemOptions): void {
        const { _id, name, quantity, unitPrice: price, tax, hsnOrSacCode } = itemData;
        const { isInclusiveofGst, discountType, discountValue, discountedBy, promotionLabel, promotionLabelKey } = options || {
            startDate: this.invoice.invoiceDate,
            isInclusiveofGst: this.invoice.isInclusiveofGst,
            discountedBy: undefined,
            promotionLabel: undefined,
            promotionLabelKey: undefined,
        };

        const { unitPrice, totalPrice, discountAmount, cartDiscountAmount, taxAmount, finalPrice } = this.calculateItemPricing({
            quantity,
            price: (price * quantity) || 0,
            taxRate: tax || 0,
            itemDiscount: {
                type: discountType,
                value: discountValue || 0,
            },
        });

        const customPackageItem: IInvoiceCustomPackageItem = {
            customPackageId: _id as Types.ObjectId,
            packageName: name,
            quantity,
            price: price,
            unitPrice: unitPrice,
            discountType: discountType && (discountValue || 0) > 0 ? discountType : undefined,
            discountValue: (discountValue || 0) > 0 ? discountValue : undefined,
            discountExcludeCart: (discountAmount || 0),
            discountIncludeCart: (cartDiscountAmount || 0),
            finalPrice: finalPrice,
            hsnOrSacCode: hsnOrSacCode || "",
            tax: tax || 0,
            gstAmount: (taxAmount || 0)
        };

        this.invoice.customPackageItems.push(customPackageItem);
    }

    returnCustomPackageItem(itemData: ICustomPackageDocument, amount: number, options?: IPurchaseItemOptions): void {
        const { _id, name, quantity, tax, hsnOrSacCode } = itemData;

        const { unitPrice, totalPrice, discountAmount, cartDiscountAmount, taxAmount, finalPrice } = this.calculateItemPricing({
            quantity,
            price: amount || 0,
            taxRate: tax || 0,
            itemDiscount: {
                type: options?.discountType,
                value: options?.discountValue || 0,
            },
        });

        const customPackageItem: IInvoiceReturnItems = {
            invoiceId: this.invoice._id as Types.ObjectId,
            packageId: _id as Types.ObjectId,
            purchaseId: null,
            packageName: name,
            quantity,
            unitPrice: unitPrice,
            price: totalPrice,
            tax: taxAmount,
            hsnOrSacCode: hsnOrSacCode || "",
            isInclusiveGst: this.invoice.isInclusiveofGst,
            basePrice: unitPrice,
            gstAmount: taxAmount,
            totalPrice: finalPrice,
            itemType: ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE,
        };

        this.invoice.returnItems.push(customPackageItem);
    }

    returnServiceItem(itemData: IPricing, amount: number, quantity: number, options?: IPurchaseItemOptions): void {
        const { _id, name, itemType, tax, hsnOrSacCode, expiredInDays, durationUnit, membershipId, promotion, pricingIds, isBundledPricing, services } = itemData;
        const { startDate, isInclusiveofGst: isInclusiveOfGst, discountType, discountValue, discountedBy, promotionLabel, promotionLabelKey } = options || {
            startDate: this.invoice.invoiceDate,
            isInclusiveofGst: this.invoice.isInclusiveofGst,
        };

        const { unitPrice, totalPrice, discountAmount, cartDiscountAmount, taxAmount, finalPrice } = this.calculateItemPricing({
            quantity,
            price: amount || 0,
            taxRate: tax || 0,
            itemDiscount: {
                type: discountType,
                value: discountValue || 0,
            },
        });
        const returnItem: IInvoiceReturnItems = {
            invoiceId: this.invoice._id as Types.ObjectId,
            packageId: _id as Types.ObjectId,
            purchaseId: null,
            packageName: name,
            quantity,
            unitPrice: unitPrice,
            price: totalPrice,
            tax: taxAmount,
            hsnOrSacCode: hsnOrSacCode || "",
            isInclusiveGst: this.invoice.isInclusiveofGst,
            basePrice: unitPrice,
            gstAmount: taxAmount,
            totalPrice: finalPrice,
            itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
            startDate: startDate,
            endDate: this.calculateEndDate(startDate, expiredInDays, durationUnit),
            expireIn: expiredInDays,
            durationUnit: durationUnit,
        };

        this.invoice.returnItems.push(returnItem);
    }

    returnProductItem(product: IProduct, inventory: IInventory, amount: number, quantity: number, variantId?: string, options?: IPurchaseItemOptions): void {
        const { salePrice, mrp } = inventory;
        const { hsn: hsnOrSacCode, gst: taxRate } = product;
        const { isInclusiveofGst: isInclusiveOfGst, discountType, discountValue, discountedBy, promotionLabel, promotionLabelKey } = options || {
            startDate: this.invoice.invoiceDate,
            isInclusiveofGst: this.invoice.isInclusiveofGst,
            discountedBy: undefined,
            promotionLabel: undefined,
            promotionLabelKey: undefined,
        };

        const { unitPrice, totalPrice, discountAmount, cartDiscountAmount, taxAmount, finalPrice } = this.calculateItemPricing({
            quantity,
            price: amount || 0,
            taxRate: taxRate || 0,
            itemDiscount: {
                type: discountType,
                value: discountValue || 0,
            },
        });

        const returnItem: IInvoiceReturnItems = {
            invoiceId: this.invoice._id as Types.ObjectId,
            packageId: product._id as Types.ObjectId,
            purchaseId: null,
            packageName: product.name,
            quantity,
            unitPrice: unitPrice,
            price: totalPrice,
            tax: taxAmount,
            hsnOrSacCode: product.hsn || "",
            isInclusiveGst: this.invoice.isInclusiveofGst,
            basePrice: unitPrice,
            gstAmount: taxAmount,
            totalPrice: finalPrice,
            itemType: ENUM_PRODUCT_ITEM_TYPE.PRODUCT,
        };

        this.invoice.returnItems.push(returnItem);
    }

    applyVoucherDiscount(voucher: IPurchase, amount: number): IPurchase {
        const previousVoucherBalance = voucher.voucherAmount - voucher.amountConsumed;
        if ((previousVoucherBalance - amount) <= 0) {
            return null;
        }
        const remainingVoucherAmount = previousVoucherBalance - amount;
        const voucherRedemption = new VoucherRedemption({
            purchaseId: voucher._id,
            userId: voucher.userId,
            organizationId: voucher.organizationId,
            usedVoucherAmount: amount,
            remainingVoucherAmount: remainingVoucherAmount,
            previousVoucherBalance: previousVoucherBalance,
            invoiceId: this.invoice._id,
        });

        voucher.amountConsumed = amount + voucher.amountConsumed;
        if (voucher.amountConsumed >= voucher.voucherAmount) {
            voucher.isExpired = true;
            voucher.isActive = false;
        }

        this.invoice.voucherDiscount = amount;
        this.invoice.voucherDetails = {
            voucherIds: [voucher._id as Types.ObjectId],
            voucherCodes: [voucher.voucherCode],
            totalVoucherAmount: previousVoucherBalance,
            usedVoucherAmount: amount,
            remainingVoucherAmount: remainingVoucherAmount,
            appliedOn: new Date(),
        };

        this.appliedVoucherItem = voucher;
        this.voucherRedemptions.push(voucherRedemption);
        return voucher;
    }

    execute(): InvoiceGenerator {

        // Calculate cart discount before gst
        this.calculateCartDiscount();

        // Calculate total amount after gst
        this.calculateTotalAmount();

        this.isCalculated = true;
        return this;
    }

    async save(options?: SaveOptions): Promise<IInvoice> {
        let session = options?.session;
        const sessionIsExternal = !!options?.session;

        if (!sessionIsExternal) {
            session = await mongoose.startSession();
            session.startTransaction();
        }

        try {
            if (!this.isCalculated) {
                this.execute();
            }

            for (const item of this.purchaseItems) {
                await item.save({ ...options, session });
            }

            if (this.voucherRedemptions.length > 0) {
                await VoucherRedemption.insertMany(this.voucherRedemptions, { session });
            }

            if (this.appliedVoucherItem) {
                await this.appliedVoucherItem.save({ ...options, session });
            }

            await this.invoice.save({ ...options, session });

            if (!sessionIsExternal) {
                await session.commitTransaction();
            }
            return this.invoice;

        } catch (error) {
            if (!sessionIsExternal) {
                await session.abortTransaction();
            }
            throw error;
        } finally {
            if (!sessionIsExternal) {
                await session.endSession();
            }
        }
    }



    /**
     * Calculate cart discount before gst
     */
    private calculateCartDiscount() {
        let cartLevelDiscount = 0;
        const { cartDiscountType, cartDiscount, isInclusiveofGst } = this.invoice;

        if (!cartDiscountType || !cartDiscount || cartDiscount <= 0) {
            return this;
        }

        const validItems: any[] = [].concat(this.invoice.purchaseItems, this.invoice.purchaseItems, this.invoice.customPackageItems);
        const itemsAfterItemDiscount = validItems.map((item: IInvoicePurchaseItem | IInvoiceProductItem | IInvoiceCustomPackageItem) => ({
            item,
            amountAfterItemDiscount: (item.price * item.quantity) - ((item.discountExcludeCart || 0)) - ((item.voucherDiscountAmount || 0))
        }));
        const totalAmountAfterItemDiscount = itemsAfterItemDiscount.reduce(
            (sum, { amountAfterItemDiscount }) => sum + amountAfterItemDiscount,
            0
        );


        if (cartDiscountType === ENUM_DISCOUNT_TYPE.PERCENTAGE && cartDiscount && cartDiscount > 0) {
            // Apply percentage discount to each valid item individually
            for (const { item, amountAfterItemDiscount } of itemsAfterItemDiscount) {

                if (amountAfterItemDiscount > 0) {
                    // Calculate cart discount amount for this item (after return discount)
                    const itemCartDiscount = Number(((cartDiscount / 100) * amountAfterItemDiscount));
                    const cartDiscountAmount = Math.min(itemCartDiscount, amountAfterItemDiscount);
                    cartLevelDiscount += cartDiscountAmount;

                    const calculation = this.calculateItemPricing({
                        quantity: 1,
                        price: item.price * item.quantity,
                        taxRate: item.tax,
                        itemDiscount: {
                            type: ENUM_DISCOUNT_TYPE.FLAT,
                            value: item.discountExcludeCart + cartDiscountAmount + item.voucherDiscountAmount,
                        },
                    });

                    item.unitPrice = calculation.unitPrice;
                    item.gstAmount = calculation.taxAmount;
                    item.discountIncludeCart = cartDiscountAmount;

                }
            }
        } else if (cartDiscountType === ENUM_DISCOUNT_TYPE.FLAT && cartDiscount && cartDiscount > 0) {
            // Distribute flat discount proportionally among valid items
            let allocatedDiscount = 0;

            for (let i = 0; i < itemsAfterItemDiscount.length; i++) {
                const { item, amountAfterItemDiscount } = itemsAfterItemDiscount[i];
                if (amountAfterItemDiscount > 0) {
                    let cartDiscountAmount = 0;

                    // Handle last item to ensure exact total
                    if (i === itemsAfterItemDiscount.length - 1) {
                        cartDiscountAmount = Math.min(
                            cartDiscount - allocatedDiscount,
                            amountAfterItemDiscount
                        );
                    } else {
                        const ratio = amountAfterItemDiscount / totalAmountAfterItemDiscount;
                        cartDiscountAmount = Number((ratio * cartDiscount));
                        cartDiscountAmount = Math.min(cartDiscountAmount, amountAfterItemDiscount);
                    }

                    allocatedDiscount += cartDiscountAmount;
                    cartLevelDiscount += cartDiscountAmount;

                    const calculation = this.calculateItemPricing({
                        quantity: 1,
                        price: (item.price * item.quantity),
                        taxRate: item.tax,
                        itemDiscount: {
                            type: ENUM_DISCOUNT_TYPE.FLAT,
                            value: item.discountExcludeCart + cartDiscountAmount + item.voucherDiscountAmount,
                        },
                    });

                    item.unitPrice = calculation.unitPrice;
                    item.gstAmount = calculation.taxAmount;
                    item.discountIncludeCart = cartDiscountAmount;
                }
            }
        }

        this.invoice.cartDiscountAmount = cartLevelDiscount;
        return this;
    }


    private calculateTotalAmount() {
        // Get all processed items
        const processedItems = [
            ...this.invoice.purchaseItems,
            ...this.invoice.productItem,
            ...this.invoice.customPackageItems
        ];

        // Calculate return amounts
        let totalEffectiveBasePriceToRefund = 0;
        let totalEffectiveGstPriceToRefund = 0;
        let totalEffectivePriceToRefund = 0;
        let totalEffectiveReturnUnitPrice = 0;

        this.invoice.returnItems?.forEach(item => {
            totalEffectiveBasePriceToRefund += item.basePrice || 0;
            totalEffectiveGstPriceToRefund += item.gstAmount || 0;
            totalEffectivePriceToRefund += item.totalPrice || 0;
            totalEffectiveReturnUnitPrice += item.unitPrice || 0;
        });

        // Calculate discounts
        const itemLevelDiscount = processedItems.reduce((sum, item) => sum + (item.discountExcludeCart || 0), 0);
        const cartLevelDiscount = this.invoice.cartDiscountAmount || 0;
        const voucherLevelDiscount = this.invoice.voucherDiscount || 0;

        // Calculate total tax after applying all discounts
        const totalTax = (processedItems.reduce((sum, item) => sum + (item.gstAmount || 0), 0)) - (totalEffectiveGstPriceToRefund || 0);

        // Calculate cart-level totals including return and voucher discounts
        const totalDiscount = itemLevelDiscount + cartLevelDiscount;

        // Calculate grand total and other values
        const voucherApplied = voucherLevelDiscount || 0;
        // Calculate total after GST first, then subtract voucher and returns
        const totalAfterGstBeforeVoucher = processedItems.reduce((sum, item) => {
            const itemPrice = (item.price || 0) * (item.quantity || 1);
            const itemDiscount = item.discountExcludeCart || 0;
            const itemTax = item.gstAmount || 0;
            // return sum + (itemPrice - itemDiscount);
            return sum + item.finalPrice;
        }, 0);

        const totalAmountAfterGst = totalAfterGstBeforeVoucher - voucherApplied - (totalEffectivePriceToRefund || 0);
        const roundOff = totalAmountAfterGst - Math.floor(totalAmountAfterGst);
        const grandTotal = Math.floor(totalAmountAfterGst);

        // Format values for consistency - use integers for monetary values
        const formattedSubtotal = processedItems.reduce((sum, item) => {
            const itemPrice = (item.price || 0) * (item.quantity || 1);
            const itemDiscount = item.discountExcludeCart || 0;
            return sum + (itemPrice - itemDiscount);
        }, 0) - (totalEffectiveBasePriceToRefund || 0);

        const formattedItemDiscount = Number(itemLevelDiscount);
        const formattedCartDiscount = Number(cartLevelDiscount);
        const formattedTotalDiscount = Number(totalDiscount);
        const formattedTax = Number(totalTax);

        // Set invoice values
        this.invoice.subTotal = formattedSubtotal;
        this.invoice.itemDiscount = formattedItemDiscount;
        this.invoice.totalGstValue = formattedTax;
        this.invoice.totalAmountAfterGst = totalAmountAfterGst;
        this.invoice.roundOff = roundOff;
        this.invoice.grandTotal = grandTotal;
        this.invoice.returnDiscount = totalEffectiveReturnUnitPrice;
        this.invoice.amountPaid = totalAmountAfterGst;
        this.invoice.amountInWords = this.convertAmountToWords(grandTotal);

        return this;
    }

    convertAmountToWords(amount) {
        const amountInWords = numberToWords.toWords(amount);
        return amountInWords.charAt(0).toUpperCase() + amountInWords.slice(1) + " Rupees Only";
    }

    private calculateEndDate(startDate: Date, expireIn: number, durationUnit: string): Date {
        const endDate = new Date(startDate);

        switch (durationUnit) {
            case ENUM_DURATION_UNIT.DAYS:

                endDate.setDate(endDate.getDate() + expireIn);
                break;
            case ENUM_DURATION_UNIT.MONTHS:
                endDate.setMonth(endDate.getMonth() + expireIn);
                break;
            case ENUM_DURATION_UNIT.YEARS:
                endDate.setFullYear(endDate.getFullYear() + expireIn);
                break;
            default:
                throw new Error(`Invalid duration unit: ${durationUnit}`);
        }

        return endDate;
    }

    /**
   * Calculate pricing for a single item including discounts and taxes
   */
    private calculateItemPricing(params: {
        quantity: number;
        price: number;
        taxRate: number;
        itemDiscount?: { type: ENUM_DISCOUNT_TYPE; value: number };
        cartDiscount?: { type: string; value: number; amount: number };
    }) {
        const { quantity, price, taxRate, itemDiscount, cartDiscount } = params;

        // Calculate total price
        // quantity is removed as the price is already the total price from csv
        const totalPrice = price // * quantity;
        let unitPrice = price;

        // Initialize discount values
        let itemDiscountAmount = 0;
        let itemDiscountType: string | undefined;
        let itemDiscountValue: number | undefined;

        let cartDiscountAmount = cartDiscount?.amount || 0;
        let cartDiscountType = cartDiscount?.type;
        let cartDiscountValue = cartDiscount?.value;

        // Apply item-level discount if available
        if (itemDiscount && itemDiscount.type && itemDiscount.value > 0) {
            if (itemDiscount.type === ENUM_DISCOUNT_TYPE.PERCENTAGE) {
                // Calculate percentage discount
                itemDiscountAmount = Number(((itemDiscount.value / 100) * totalPrice));
                itemDiscountType = ENUM_DISCOUNT_TYPE.PERCENTAGE;
                itemDiscountValue = itemDiscount.value;
            } else if (itemDiscount.type === ENUM_DISCOUNT_TYPE.FLAT) {
                // Calculate flat discount
                itemDiscountAmount = Math.min(itemDiscount.value * quantity, totalPrice);
                itemDiscountType = ENUM_DISCOUNT_TYPE.FLAT;
                itemDiscountValue = itemDiscount.value;
            }

            // Ensure discount doesn't exceed total price
            itemDiscountAmount = Math.min(itemDiscountAmount, totalPrice);
        }

        // Calculate amount after item-level discount
        const amountAfterItemDiscount = totalPrice - itemDiscountAmount;

        // Ensure cart discount doesn't exceed remaining amount
        cartDiscountAmount = Math.min(cartDiscountAmount || 0, amountAfterItemDiscount);

        // Calculate total discount
        const totalDiscountAmount = itemDiscountAmount + cartDiscountAmount;

        // Calculate tax amount (on price after all discounts)
        const taxableAmount = totalPrice - totalDiscountAmount;
        let taxAmount = 0;
        let finalPrice = 0;
        let discountedPrice = 0;
        if (this.invoice.isInclusiveofGst) {
            taxAmount = Number((taxableAmount * taxRate) / (100 + taxRate));
            discountedPrice = taxableAmount - taxAmount;
            finalPrice = taxableAmount;
            unitPrice = (discountedPrice + totalDiscountAmount) / quantity;
            // unitPrice = unitPrice - Number((unitPrice * taxRate) / (100 + taxRate));
            // unitPrice = unitPrice - Number((unitPrice * taxRate) / (100 + taxRate));
        } else {
            taxAmount = Number(((taxRate / 100) * taxableAmount));
            finalPrice = totalPrice - totalDiscountAmount + taxAmount;
            discountedPrice = totalPrice - totalDiscountAmount;
            unitPrice = (discountedPrice + totalDiscountAmount) / quantity;
        }

        return {
            quantity,
            price,
            unitPrice,
            totalPrice,
            discountAmount: itemDiscountAmount,
            discountType: itemDiscountType,
            discountValue: itemDiscountValue,
            cartDiscountAmount,
            cartDiscountType,
            cartDiscountValue,
            totalDiscountAmount,
            taxRate,
            taxAmount,
            discountedPrice,
            finalPrice,
        };
    }

}
