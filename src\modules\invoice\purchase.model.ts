import mongoose, { Document, Schema, Types } from 'mongoose';
import { PaymentStatus } from '../../utils/enums/payment.enum';
import { SessionType } from '../../utils/enums/session-type.enum';
import { ENUM_PRODUCT_ITEM_TYPE } from '../../common/enums/enums';

export interface ISuspension extends Document {
  isResumed?: boolean;
  fromDate: Date;
  endDate: Date;
  notes: string;
}

export interface IPurchase extends Document {
  invoiceId?: Types.ObjectId;
  invoiceItemId: string;
  userId: Types.ObjectId;
  sponsorUser?: Types.ObjectId;
  consumers: Types.ObjectId[];
  removedConsumers: Types.ObjectId[];
  itemType: ENUM_PRODUCT_ITEM_TYPE;
  packageId: Types.ObjectId;
  bundledPricingId?: Types.ObjectId;
  organizationId: Types.ObjectId;
  facilityId: Types.ObjectId;
  purchasedBy: Types.ObjectId;
  membershipId?: Types.ObjectId;
  voucherAmount?: number;
  amountConsumed?: number;
  voucherCode?: string;
  purchaseDate: Date;
  paymentStatus: PaymentStatus;
  isExpired: boolean;
  sharePass?: boolean;
  sharedBy?: Types.ObjectId;
  exchangedInvoiceId?: Types.ObjectId;
  exchangeDate?: Date;
  sessionType: SessionType;
  totalSessions: number;
  sessionConsumed?: number;
  sessionShared?: number;
  sessionPerDay?: number;
  dayPassLimit?: number;
  isActive?: boolean;
  startDate: Date;
  endDate: Date;
  suspensions?: ISuspension[];
  createdAt: Date;
  updatedAt: Date;
}

// Define schemas
const SuspensionsSchema = new Schema<ISuspension>({
  isResumed: { type: Boolean, required: false, default: undefined },
  fromDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  notes: { type: String, required: false, default: "" }
}, { timestamps: true, versionKey: false });

// Define the main Purchase schema
const PurchaseSchema = new Schema({
  invoiceId: { type: Schema.Types.ObjectId, required: false, ref: "Invoice" },
  invoiceItemId: { type: String, required: true },
  userId: { type: Schema.Types.ObjectId, required: true, ref: "User" },
  sponsorUser: { type: Schema.Types.ObjectId, required: false, ref: "User" },
  consumers: [{ type: Schema.Types.ObjectId, ref: 'User', required: true }],
  removedConsumers: [{ type: Schema.Types.ObjectId, ref: 'User', required: true }],
  packageId: { type: Schema.Types.ObjectId, required: true, ref: 'Pricing' },
  itemType: { type: String, enum: Object.values(ENUM_PRODUCT_ITEM_TYPE), required: true },
  bundledPricingId: { type: Schema.Types.ObjectId, required: false, ref: 'Pricing' },
  organizationId: { type: Schema.Types.ObjectId, required: true, ref: "User" },
  facilityId: { type: Schema.Types.ObjectId, required: true, ref: 'Facility' },
  purchasedBy: { type: Schema.Types.ObjectId, required: false, ref: 'User' },
  membershipId: { type: Schema.Types.ObjectId, required: false, default: null, ref: 'membership' },
  voucherAmount: { type: Number, required: false },
  voucherCode: {
    type: String, required: function () {
      return this.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER;
    }
  },
  amountConsumed: { type: Number, required: false },
  purchaseDate: { type: Date, required: true },
  paymentStatus: { type: String, required: true, enum: Object.values(PaymentStatus) },
  isExpired: { type: Boolean, required: true },
  sharePass: { type: Boolean, required: false, default: false },
  sharedBy: { type: Schema.Types.ObjectId, required: false, ref: "User" },
  exchangedInvoiceId: { type: Schema.Types.ObjectId, required: false, ref: "Invoice" },
  exchangeDate: { type: Date, required: false },
  sessionType: {
    type: String, enum: Object.values(SessionType), required: function () {
      return this.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE;
    }
  },
  totalSessions: {
    type: Number, required: function () {
      return this.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE;
    }
  },
  sessionConsumed: {
    type: Number, required: function () {
      return this.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE;
    }, default: 0
  },
  sessionShared: { type: Number, required: false },
  sessionPerDay: { type: Number, required: false },
  dayPassLimit: { type: Number, required: false },
  isActive: { type: Boolean, required: false, default: true },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  suspensions: { type: [SuspensionsSchema], required: false, default: [] }
}, { timestamps: true });

// Create and export the model
export const PURCHASE_COLLECTION = 'purchases';
export const Purchase = mongoose.model<IPurchase>('Purchase', PurchaseSchema, PURCHASE_COLLECTION);

PurchaseSchema.index({ voucherCode: 1, organizationId: 1 }, { unique: true, sparse: true });

PurchaseSchema.pre<IPurchase>('save', function (next) {
  this.voucherCode = this.voucherCode?.trim() && this.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER ? this.voucherCode?.trim() : undefined;
  next();
});
